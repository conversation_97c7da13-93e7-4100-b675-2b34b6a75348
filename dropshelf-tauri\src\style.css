/* DropShelf Tauri 样式文件 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #2a2a2a;
    margin: 0;
    padding: 10px;
    overflow: hidden;
    user-select: none;
    -webkit-user-select: none;
    color: white;
}

.hidden {
    display: none !important;
}

/* 启动消息样式 */
.startup-message {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.startup-content {
    background: rgba(60, 60, 60, 0.95);
    border: 1px solid rgba(100, 100, 100, 0.8);
    border-radius: 12px;
    padding: 20px 30px;
    text-align: center;
    color: white;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.startup-icon {
    font-size: 32px;
    margin-bottom: 10px;
}

.startup-text {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
}

.startup-subtitle {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
}

/* 文件架容器 */
.shelf-container {
    position: relative;
    background: #404040;
    border: 2px solid #606060;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    padding: 10px;
    width: 100%;
    max-width: 280px;
}

/* 文件集合组件 */
.file-collection {
    width: 120px;
    height: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.file-collection:hover {
    background: rgba(80, 80, 80, 0.8);
    border-color: rgba(120, 120, 120, 1);
}

.collection-icon {
    font-size: 24px;
    margin-bottom: 4px;
}

.collection-count {
    font-size: 9px;
    color: rgba(255, 255, 255, 0.9);
    text-align: center;
}

/* 展开的文件列表 */
.expanded-list {
    position: absolute;
    top: 85px;
    left: 0;
    min-width: 170px;
    max-width: 190px;
    max-height: 320px;
    background: rgba(50, 50, 50, 0.95);
    border: 1px solid rgba(100, 100, 100, 0.6);
    border-radius: 6px;
    margin-top: 5px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
}

.file-list-container {
    padding: 5px;
    max-height: 200px;
    overflow-y: auto;
}

.file-list {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

/* 文件项 */
.file-item {
    background: rgba(70, 70, 70, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    cursor: pointer;
    transition: background 0.2s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-item:hover {
    background: rgba(90, 90, 90, 0.9);
}

/* 占位符 */
.placeholder {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(40, 40, 40, 0.8);
    border: 2px dashed rgba(100, 100, 100, 0.5);
    border-radius: 8px;
}

.placeholder-content {
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
}

.placeholder-icon {
    font-size: 48px;
    margin-bottom: 10px;
}

.placeholder-text {
    font-size: 14px;
    margin-bottom: 10px;
}

.test-button {
    background: rgba(80, 120, 200, 0.8);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background 0.2s ease;
}

.test-button:hover {
    background: rgba(100, 140, 220, 0.9);
}

/* 滚动条样式 */
.file-list-container::-webkit-scrollbar {
    width: 6px;
}

.file-list-container::-webkit-scrollbar-track {
    background: rgba(30, 30, 30, 0.5);
    border-radius: 3px;
}

.file-list-container::-webkit-scrollbar-thumb {
    background: rgba(100, 100, 100, 0.5);
    border-radius: 3px;
}

.file-list-container::-webkit-scrollbar-thumb:hover {
    background: rgba(120, 120, 120, 0.7);
}
