{"rustc": 16591470773350601817, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2225463790103693989, "path": 18346117312317200290, "deps": [[561782849581144631, "html5ever", false, 1747545318818591575], [1200537532907108615, "url<PERSON><PERSON>n", false, 15833402462459185345], [3060637413840920116, "proc_macro2", false, 303717936180483038], [3129130049864710036, "memchr", false, 17125720940414977180], [3150220818285335163, "url", false, 7265444856805421200], [3191507132440681679, "serde_untagged", false, 8847021120861022935], [4899080583175475170, "semver", false, 12483454171726233177], [5986029879202738730, "log", false, 16679470630505032336], [6213549728662707793, "serde_with", false, 6376987924595326566], [6262254372177975231, "kuchiki", false, 9895712597134857696], [6606131838865521726, "ctor", false, 18091742879657052143], [6913375703034175521, "schemars", false, 6427172178636291765], [7170110829644101142, "json_patch", false, 12137466159561387950], [8319709847752024821, "uuid", false, 9718985504502034272], [9010263965687315507, "http", false, 997752676421124756], [9451456094439810778, "regex", false, 10331129114192926143], [9689903380558560274, "serde", false, 6321106767037116303], [10806645703491011684, "thiserror", false, 11504625584015650060], [11655476559277113544, "cargo_metadata", false, 6447925149330669681], [11989259058781683633, "dunce", false, 17936449102137258041], [13625485746686963219, "anyhow", false, 17869629950161525719], [14132538657330703225, "brotli", false, 15643895828906240256], [15367738274754116744, "serde_json", false, 8383117432936140794], [15609422047640926750, "toml", false, 16738967182127994125], [15622660310229662834, "walkdir", false, 10049784400214970020], [17146114186171651583, "infer", false, 2263749437181854576], [17155886227862585100, "glob", false, 9992052714327231586], [17186037756130803222, "phf", false, 16401682017881137445], [17990358020177143287, "quote", false, 15754227166550723905]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-3d047454329f6af3\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}