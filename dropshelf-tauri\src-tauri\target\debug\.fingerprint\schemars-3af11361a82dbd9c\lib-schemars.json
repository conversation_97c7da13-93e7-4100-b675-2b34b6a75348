{"rustc": 16591470773350601817, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 2225463790103693989, "path": 6880556076000324127, "deps": [[3150220818285335163, "url", false, 7265444856805421200], [6913375703034175521, "build_script_build", false, 11570022240484371799], [8319709847752024821, "uuid1", false, 9718985504502034272], [9122563107207267705, "dyn_clone", false, 3438039408505110246], [9689903380558560274, "serde", false, 6321106767037116303], [14923790796823607459, "indexmap", false, 7671017423491358912], [15367738274754116744, "serde_json", false, 8383117432936140794], [16071897500792579091, "schemars_derive", false, 12109284156081563178]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\schemars-3af11361a82dbd9c\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}