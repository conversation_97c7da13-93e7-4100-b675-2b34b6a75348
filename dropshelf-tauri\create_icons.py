#!/usr/bin/env python3
"""
创建 DropShelf 应用图标
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon(size, output_path):
    """创建指定尺寸的图标"""
    # 创建图像
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制背景圆形
    margin = size // 8
    draw.ellipse([margin, margin, size-margin, size-margin], 
                fill=(60, 60, 60, 200), outline=(100, 100, 100, 255), width=2)
    
    # 绘制文件夹图标
    folder_size = size // 2
    folder_x = (size - folder_size) // 2
    folder_y = (size - folder_size) // 2
    
    # 简单的文件夹形状
    draw.rectangle([folder_x, folder_y + folder_size//4, 
                   folder_x + folder_size, folder_y + folder_size], 
                  fill=(255, 255, 255, 200))
    
    # 文件夹顶部
    draw.rectangle([folder_x, folder_y, 
                   folder_x + folder_size//2, folder_y + folder_size//4], 
                  fill=(255, 255, 255, 200))
    
    # 保存图标
    img.save(output_path, 'PNG')
    print(f"创建图标: {output_path} ({size}x{size})")

def main():
    """主函数"""
    icons_dir = "src-tauri/icons"
    os.makedirs(icons_dir, exist_ok=True)
    
    # 创建不同尺寸的图标
    sizes = [32, 128, 256, 512]
    
    for size in sizes:
        if size == 128:
            create_icon(size, f"{icons_dir}/128x128.png")
            create_icon(size * 2, f"{icons_dir}/<EMAIL>")
        else:
            create_icon(size, f"{icons_dir}/{size}x{size}.png")
    
    # 创建 ICO 文件（Windows）
    try:
        img_32 = Image.open(f"{icons_dir}/32x32.png")
        img_32.save(f"{icons_dir}/icon.ico", format='ICO', sizes=[(32, 32)])
        print("创建 Windows ICO 图标")
    except Exception as e:
        print(f"创建 ICO 图标失败: {e}")
    
    # 创建 ICNS 文件（macOS）
    try:
        img_512 = Image.open(f"{icons_dir}/512x512.png")
        img_512.save(f"{icons_dir}/icon.icns", format='ICNS')
        print("创建 macOS ICNS 图标")
    except Exception as e:
        print(f"创建 ICNS 图标失败: {e}")

if __name__ == "__main__":
    main()
