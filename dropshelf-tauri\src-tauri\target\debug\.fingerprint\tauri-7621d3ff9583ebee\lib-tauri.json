{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 14044876287012107893, "deps": [[40386456601120721, "percent_encoding", false, 9522742609963403041], [442785307232013896, "tauri_runtime", false, 103214334099100806], [1200537532907108615, "url<PERSON><PERSON>n", false, 11792143963492262519], [3150220818285335163, "url", false, 16151976542863992648], [4143744114649553716, "raw_window_handle", false, 7444958033936336087], [4341921533227644514, "muda", false, 17534636353239888909], [4919829919303820331, "serialize_to_javascript", false, 8602769115525699556], [5986029879202738730, "log", false, 16679470630505032336], [7752760652095876438, "tauri_runtime_wry", false, 17224430324221582294], [8539587424388551196, "webview2_com", false, 17071827347805798439], [9010263965687315507, "http", false, 997752676421124756], [9228235415475680086, "tauri_macros", false, 5739218573841929110], [9538054652646069845, "tokio", false, 4103961411369455676], [9689903380558560274, "serde", false, 1695654867491534608], [9920160576179037441, "getrandom", false, 3427558626643439191], [10229185211513642314, "mime", false, 2581475215710292918], [10629569228670356391, "futures_util", false, 6582955280151940554], [10755362358622467486, "build_script_build", false, 3382514209651873442], [10806645703491011684, "thiserror", false, 11504625584015650060], [11050281405049894993, "tauri_utils", false, 349942949715664439], [11989259058781683633, "dunce", false, 17936449102137258041], [12565293087094287914, "window_vibrancy", false, 17215738060454537506], [12986574360607194341, "serde_repr", false, 9266366341455885535], [13077543566650298139, "heck", false, 10476147102614925610], [13116089016666501665, "windows", false, 16591918237836803963], [13625485746686963219, "anyhow", false, 17869629950161525719], [15367738274754116744, "serde_json", false, 1365797035968003195], [16928111194414003569, "dirs", false, 5223751243747311782], [17155886227862585100, "glob", false, 9992052714327231586]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-7621d3ff9583ebee\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}