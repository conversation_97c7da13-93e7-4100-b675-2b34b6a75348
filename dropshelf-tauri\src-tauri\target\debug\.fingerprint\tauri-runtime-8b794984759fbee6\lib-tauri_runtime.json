{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 6303096234831532250, "deps": [[442785307232013896, "build_script_build", false, 15580854490711698325], [3150220818285335163, "url", false, 16151976542863992648], [4143744114649553716, "raw_window_handle", false, 7444958033936336087], [7606335748176206944, "dpi", false, 1919892622856328729], [9010263965687315507, "http", false, 997752676421124756], [9689903380558560274, "serde", false, 1695654867491534608], [10806645703491011684, "thiserror", false, 11504625584015650060], [11050281405049894993, "tauri_utils", false, 349942949715664439], [13116089016666501665, "windows", false, 16591918237836803963], [15367738274754116744, "serde_json", false, 1365797035968003195], [16727543399706004146, "cookie", false, 583079190556046983]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-8b794984759fbee6\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}