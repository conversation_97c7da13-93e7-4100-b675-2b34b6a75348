// DropShelf Tauri 前端脚本

const { invoke } = window.__TAURI__.core;
const { listen } = window.__TAURI__.event;

class DropShelfApp {
    constructor() {
        this.fileSet = new Set(); // 存储文件路径，自动去重
        this.isExpanded = false;
        this.isVisible = false;
        
        this.initializeElements();
        this.setupEventListeners();
        this.setupTauriListeners();
        
        console.log('DropShelf 前端初始化完成');
    }

    initializeElements() {
        this.startupMessage = document.getElementById('startup-message');
        this.shelfContainer = document.getElementById('shelf-container');
        this.fileCollection = document.getElementById('file-collection');
        this.expandedList = document.getElementById('expanded-list');
        this.fileList = document.getElementById('file-list');
        this.placeholder = document.getElementById('placeholder');
        this.collectionIcon = document.querySelector('.collection-icon');
        this.collectionCount = document.querySelector('.collection-count');
        this.testButton = document.getElementById('test-button');
    }

    setupEventListeners() {
        // 文件集合点击事件
        this.fileCollection.addEventListener('click', (e) => {
            e.preventDefault();
            if (this.fileSet.size > 1) {
                this.toggleExpansion();
            } else if (this.fileSet.size === 1) {
                console.log('ℹ️ 只有1个文件，无需展开');
            } else {
                console.log('ℹ️ 没有文件');
            }
        });

        // 右键关闭
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.hideShelf();
        });

        // ESC 键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideShelf();
            }
        });

        // 拖拽事件
        document.addEventListener('dragover', (e) => {
            e.preventDefault();
        });

        document.addEventListener('drop', (e) => {
            e.preventDefault();
            this.handleFileDrop(e);
        });

        // 文件集合拖拽开始
        this.fileCollection.addEventListener('dragstart', (e) => {
            if (this.fileSet.size > 0) {
                this.startAllFilesDrag(e);
            }
        });

        this.fileCollection.draggable = true;

        // 测试按钮事件
        if (this.testButton) {
            this.testButton.addEventListener('click', async () => {
                try {
                    await invoke('test_show_shelf');
                    console.log('🧪 手动测试触发成功');
                } catch (error) {
                    console.error('测试失败:', error);
                }
            });
        }
    }

    async setupTauriListeners() {
        try {
            // 监听手势检测事件
            await listen('shake-detected', (event) => {
                console.log('检测到抖动手势:', event.payload);
                this.showShelfAt(event.payload.x, event.payload.y);
            });

            // 监听拖拽状态变化
            await listen('drag-state-changed', (event) => {
                console.log('拖拽状态变化:', event.payload);
            });

            console.log('Tauri 事件监听器设置完成');
        } catch (error) {
            console.error('设置 Tauri 监听器失败:', error);
        }
    }

    showStartupMessage() {
        this.startupMessage.classList.remove('hidden');
        setTimeout(() => {
            this.hideStartupMessage();
        }, 1000);
    }

    hideStartupMessage() {
        this.startupMessage.classList.add('hidden');
    }

    async showShelfAt(x, y) {
        if (this.isVisible) {
            console.log('Shelf 已显示，忽略');
            return;
        }

        try {
            // 窗口已经由后端显示，这里只需要更新前端状态
            this.isVisible = true;
            this.shelfContainer.classList.remove('hidden');

            // 根据文件数量显示不同内容
            if (this.fileSet.size === 0) {
                this.placeholder.classList.remove('hidden');
            } else {
                this.placeholder.classList.add('hidden');
            }

            console.log(`Shelf 前端状态已更新，位置 (${x}, ${y})`);
        } catch (error) {
            console.error('更新 Shelf 状态失败:', error);
        }
    }

    async hideShelf() {
        if (!this.isVisible) return;

        try {
            await invoke('hide_shelf');
            this.isVisible = false;
            this.shelfContainer.classList.add('hidden');
            this.placeholder.classList.add('hidden');
            
            // 收起展开状态
            if (this.isExpanded) {
                this.isExpanded = false;
                this.expandedList.classList.add('hidden');
            }
            
            console.log('Shelf 已隐藏');
        } catch (error) {
            console.error('隐藏 Shelf 失败:', error);
        }
    }

    handleFileDrop(event) {
        const files = Array.from(event.dataTransfer.files);
        if (files.length === 0) return;

        const filePaths = files.map(file => file.path);
        this.addFiles(filePaths);
        
        // 隐藏占位符
        this.placeholder.classList.add('hidden');
    }

    addFiles(filePaths) {
        let addedCount = 0;
        
        filePaths.forEach(path => {
            if (!this.fileSet.has(path)) {
                this.fileSet.add(path);
                this.addFileToList(path);
                addedCount++;
            }
        });

        if (addedCount > 0) {
            this.updateDisplay();
            console.log(`添加了 ${addedCount} 个新文件，总计 ${this.fileSet.size} 个文件`);
        } else {
            console.log('所有文件都已存在，未添加重复文件');
        }
    }

    addFileToList(filePath) {
        const fileName = filePath.split('\\').pop() || filePath.split('/').pop();
        const fileItem = document.createElement('div');
        fileItem.className = 'file-item';
        fileItem.textContent = `📄 ${fileName}`;
        fileItem.title = filePath;
        fileItem.draggable = true;

        // 单个文件拖拽
        fileItem.addEventListener('dragstart', (e) => {
            this.startSingleFileDrag(e, filePath);
        });

        // 右键删除文件
        fileItem.addEventListener('contextmenu', (e) => {
            e.stopPropagation();
            this.removeFile(filePath);
        });

        this.fileList.appendChild(fileItem);
    }

    updateDisplay() {
        const count = this.fileSet.size;
        
        if (count === 0) {
            this.collectionCount.textContent = '空';
            this.collectionIcon.textContent = '📦';
        } else if (count === 1) {
            this.collectionCount.textContent = '1 个文件';
            this.collectionIcon.textContent = '📄';
        } else {
            this.collectionCount.textContent = `${count} 个文件`;
            this.collectionIcon.textContent = '📦';
        }

        // 调整容器大小
        if (this.isExpanded && count > 0) {
            this.shelfContainer.style.width = '190px';
            this.shelfContainer.style.height = 'auto';
        } else {
            this.shelfContainer.style.width = '120px';
            this.shelfContainer.style.height = '80px';
        }
    }

    toggleExpansion() {
        this.isExpanded = !this.isExpanded;

        if (this.isExpanded) {
            this.expandedList.classList.remove('hidden');
            console.log('📂 展开文件列表');
        } else {
            this.expandedList.classList.add('hidden');
            console.log('📁 收起文件列表');
        }

        this.updateDisplay();
    }

    removeFile(filePath) {
        if (this.fileSet.has(filePath)) {
            this.fileSet.delete(filePath);

            // 从 UI 中移除
            const fileItems = this.fileList.querySelectorAll('.file-item');
            fileItems.forEach(item => {
                if (item.title === filePath) {
                    item.remove();
                }
            });

            // 检查是否需要自动收起
            if (this.fileSet.size === 0) {
                // 没有文件了，自动收起并显示占位符
                if (this.isExpanded) {
                    this.isExpanded = false;
                    this.expandedList.classList.add('hidden');
                    console.log('📁 没有文件了，自动收起');
                }
                this.placeholder.classList.remove('hidden');
            } else if (this.fileSet.size === 1 && this.isExpanded) {
                // 只剩1个文件且处于展开状态，自动收起
                this.isExpanded = false;
                this.expandedList.classList.add('hidden');
                console.log('📁 只剩1个文件，自动收起');
            }

            this.updateDisplay();
            const fileName = filePath.split('\\').pop() || filePath.split('/').pop();
            console.log(`🗑️ 移除文件: ${fileName}`);
        }
    }

    startAllFilesDrag(event) {
        if (this.fileSet.size === 0) {
            event.preventDefault();
            return;
        }

        const filePaths = Array.from(this.fileSet);
        event.dataTransfer.setData('text/plain', filePaths.join('\n'));
        event.dataTransfer.effectAllowed = 'copy';
        
        console.log(`开始拖拽 ${filePaths.length} 个文件`);
    }

    startSingleFileDrag(event, filePath) {
        event.stopPropagation();
        event.dataTransfer.setData('text/plain', filePath);
        event.dataTransfer.effectAllowed = 'copy';
        
        const fileName = filePath.split('\\').pop() || filePath.split('/').pop();
        console.log(`开始拖拽单个文件: ${fileName}`);
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.dropShelfApp = new DropShelfApp();
    
    // 显示启动消息
    window.dropShelfApp.showStartupMessage();
});
