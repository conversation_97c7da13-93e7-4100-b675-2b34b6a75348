Copy from "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\webview2-com-sys-0.37.0\\arm64\\WebView2Loader.dll" -> "C:\\Users\\<USER>\\Desktop\\试验项目\\Aug_DropShelf_new - 副本\\dropshelf-tauri\\src-tauri\\target\\debug\\build\\webview2-com-sys-964fd43d83a39a4c\\out\\arm64\\WebView2Loader.dll"
Copy from "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\webview2-com-sys-0.37.0\\arm64\\WebView2Loader.dll.lib" -> "C:\\Users\\<USER>\\Desktop\\试验项目\\Aug_DropShelf_new - 副本\\dropshelf-tauri\\src-tauri\\target\\debug\\build\\webview2-com-sys-964fd43d83a39a4c\\out\\arm64\\WebView2Loader.dll.lib"
Copy from "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\webview2-com-sys-0.37.0\\arm64\\WebView2LoaderStatic.lib" -> "C:\\Users\\<USER>\\Desktop\\试验项目\\Aug_DropShelf_new - 副本\\dropshelf-tauri\\src-tauri\\target\\debug\\build\\webview2-com-sys-964fd43d83a39a4c\\out\\arm64\\WebView2LoaderStatic.lib"
Copy from "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\webview2-com-sys-0.37.0\\x64\\WebView2Loader.dll" -> "C:\\Users\\<USER>\\Desktop\\试验项目\\Aug_DropShelf_new - 副本\\dropshelf-tauri\\src-tauri\\target\\debug\\build\\webview2-com-sys-964fd43d83a39a4c\\out\\x64\\WebView2Loader.dll"
Copy from "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\webview2-com-sys-0.37.0\\x64\\WebView2Loader.dll.lib" -> "C:\\Users\\<USER>\\Desktop\\试验项目\\Aug_DropShelf_new - 副本\\dropshelf-tauri\\src-tauri\\target\\debug\\build\\webview2-com-sys-964fd43d83a39a4c\\out\\x64\\WebView2Loader.dll.lib"
Copy from "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\webview2-com-sys-0.37.0\\x64\\WebView2LoaderStatic.lib" -> "C:\\Users\\<USER>\\Desktop\\试验项目\\Aug_DropShelf_new - 副本\\dropshelf-tauri\\src-tauri\\target\\debug\\build\\webview2-com-sys-964fd43d83a39a4c\\out\\x64\\WebView2LoaderStatic.lib"
Copy from "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\webview2-com-sys-0.37.0\\x86\\WebView2Loader.dll" -> "C:\\Users\\<USER>\\Desktop\\试验项目\\Aug_DropShelf_new - 副本\\dropshelf-tauri\\src-tauri\\target\\debug\\build\\webview2-com-sys-964fd43d83a39a4c\\out\\x86\\WebView2Loader.dll"
Copy from "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\webview2-com-sys-0.37.0\\x86\\WebView2Loader.dll.lib" -> "C:\\Users\\<USER>\\Desktop\\试验项目\\Aug_DropShelf_new - 副本\\dropshelf-tauri\\src-tauri\\target\\debug\\build\\webview2-com-sys-964fd43d83a39a4c\\out\\x86\\WebView2Loader.dll.lib"
Copy from "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\webview2-com-sys-0.37.0\\x86\\WebView2LoaderStatic.lib" -> "C:\\Users\\<USER>\\Desktop\\试验项目\\Aug_DropShelf_new - 副本\\dropshelf-tauri\\src-tauri\\target\\debug\\build\\webview2-com-sys-964fd43d83a39a4c\\out\\x86\\WebView2LoaderStatic.lib"
