{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 15657897354478470176, "path": 13483845031495053989, "deps": [[500211409582349667, "shared_child", false, 4031827490022952925], [1582828171158827377, "build_script_build", false, 9805579911996190199], [5986029879202738730, "log", false, 16679470630505032336], [9451456094439810778, "regex", false, 10331129114192926143], [9538054652646069845, "tokio", false, 4103961411369455676], [9689903380558560274, "serde", false, 1695654867491534608], [10755362358622467486, "tauri", false, 14836831967397928173], [10806645703491011684, "thiserror", false, 11504625584015650060], [11337703028400419576, "os_pipe", false, 14869171419332798441], [14564311161534545801, "encoding_rs", false, 2098255187926643286], [15367738274754116744, "serde_json", false, 1365797035968003195], [16192041687293812804, "open", false, 853298430008317500]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-shell-91298fd7b68d91f5\\dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}