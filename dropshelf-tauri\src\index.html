<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DropShelf</title>
    <link rel="stylesheet" href="style.css">
    <script type="module">
        // 确保 Tauri API 可用
        if (typeof window.__TAURI__ === 'undefined') {
            console.error('Tauri API 未加载');
        } else {
            console.log('Tauri API 已加载');
        }
    </script>
</head>
<body>
    <div id="app">
        <!-- 标题 -->
        <h2>DropShelf Tauri 版本</h2>

        <!-- 状态信息 -->
        <div id="status" style="margin-bottom: 10px; padding: 10px; background: #333; border-radius: 4px;">
            <div>状态: <span id="status-text">正在初始化...</span></div>
            <div>文件数量: <span id="file-count">0</span></div>
        </div>

        <!-- 启动消息 -->
        <div id="startup-message" class="startup-message hidden">
            <div class="startup-content">
                <div class="startup-icon">📦</div>
                <div class="startup-text">DropShelf 已启动</div>
                <div class="startup-subtitle">拖拽文件时抖动鼠标召唤</div>
            </div>
        </div>

        <!-- 文件架主界面 -->
        <div id="shelf-container" class="shelf-container hidden">
            <!-- 文件集合组件 -->
            <div id="file-collection" class="file-collection">
                <div class="collection-icon">📦</div>
                <div class="collection-count">空</div>
            </div>

            <!-- 展开的文件列表 -->
            <div id="expanded-list" class="expanded-list hidden">
                <div class="file-list-container">
                    <div id="file-list" class="file-list">
                        <!-- 文件项将动态添加到这里 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试按钮区域 -->
        <div style="margin: 10px 0; padding: 10px; background: #333; border-radius: 4px;">
            <h3>测试功能</h3>
            <button id="test-button" class="test-button">测试后端连接</button>
            <button id="add-test-file" class="test-button">添加测试文件</button>
            <button id="clear-files" class="test-button">清空文件</button>
        </div>

        <!-- 占位符消息 -->
        <div id="placeholder" class="placeholder hidden">
            <div class="placeholder-content">
                <div class="placeholder-icon">📁</div>
                <div class="placeholder-text">拖拽文件到这里</div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
