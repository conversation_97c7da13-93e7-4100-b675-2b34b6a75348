<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DropShelf</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="app">
        <!-- 启动消息 -->
        <div id="startup-message" class="startup-message hidden">
            <div class="startup-content">
                <div class="startup-icon">📦</div>
                <div class="startup-text">DropShelf 已启动</div>
                <div class="startup-subtitle">拖拽文件时抖动鼠标召唤</div>
            </div>
        </div>

        <!-- 文件架主界面 -->
        <div id="shelf-container" class="shelf-container hidden">
            <!-- 文件集合组件 -->
            <div id="file-collection" class="file-collection">
                <div class="collection-icon">📦</div>
                <div class="collection-count">空</div>
            </div>

            <!-- 展开的文件列表 -->
            <div id="expanded-list" class="expanded-list hidden">
                <div class="file-list-container">
                    <div id="file-list" class="file-list">
                        <!-- 文件项将动态添加到这里 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 占位符消息 -->
        <div id="placeholder" class="placeholder hidden">
            <div class="placeholder-content">
                <div class="placeholder-icon">📁</div>
                <div class="placeholder-text">拖拽文件到这里</div>
                <button id="test-button" class="test-button">测试显示</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
