C:\Users\<USER>\Desktop\试验项目\Aug_DropShelf_new - 副本\dropshelf-tauri\src-tauri\target\debug\deps\dropshelf_tauri.exe: src\main.rs src\gesture_detector.rs src\file_manager.rs C:\Users\<USER>\Desktop\试验项目\Aug_DropShelf_new\ -\ 副本\dropshelf-tauri\src-tauri\target\debug\build\dropshelf-tauri-458aa21313b15ca5\out/948fdfbb31fa4caa7059f365d1fce874323026a81c25fb00ce174ea2738700a3

C:\Users\<USER>\Desktop\试验项目\Aug_DropShelf_new - 副本\dropshelf-tauri\src-tauri\target\debug\deps\dropshelf_tauri.d: src\main.rs src\gesture_detector.rs src\file_manager.rs C:\Users\<USER>\Desktop\试验项目\Aug_DropShelf_new\ -\ 副本\dropshelf-tauri\src-tauri\target\debug\build\dropshelf-tauri-458aa21313b15ca5\out/948fdfbb31fa4caa7059f365d1fce874323026a81c25fb00ce174ea2738700a3

src\main.rs:
src\gesture_detector.rs:
src\file_manager.rs:
C:\Users\<USER>\Desktop\试验项目\Aug_DropShelf_new\ -\ 副本\dropshelf-tauri\src-tauri\target\debug\build\dropshelf-tauri-458aa21313b15ca5\out/948fdfbb31fa4caa7059f365d1fce874323026a81c25fb00ce174ea2738700a3:

# env-dep:CARGO_PKG_AUTHORS=DropShelf Team
# env-dep:CARGO_PKG_DESCRIPTION=DropShelf - Windows productivity tool
# env-dep:CARGO_PKG_NAME=dropshelf-tauri
# env-dep:OUT_DIR=C:\\Users\\<USER>\\Desktop\\试验项目\\Aug_DropShelf_new - 副本\\dropshelf-tauri\\src-tauri\\target\\debug\\build\\dropshelf-tauri-458aa21313b15ca5\\out
