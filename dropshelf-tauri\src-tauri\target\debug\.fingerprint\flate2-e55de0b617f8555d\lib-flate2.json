{"rustc": 16591470773350601817, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 2225463790103693989, "path": 17590331849100748487, "deps": [[4675849561795547236, "miniz_oxide", false, 16856894648973433824], [5466618496199522463, "crc32fast", false, 15897057728657105538]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\flate2-e55de0b617f8555d\\dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}