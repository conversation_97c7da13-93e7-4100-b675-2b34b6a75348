C:\Users\<USER>\Desktop\试验项目\Aug_DropShelf_new - 副本\dropshelf-tauri\src-tauri\target\debug\build\tauri-d5cf51a73d60648f\build_script_build-d5cf51a73d60648f.exe: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-2.5.1\build.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-2.5.1\./mobile/proguard-tauri.pro

C:\Users\<USER>\Desktop\试验项目\Aug_DropShelf_new - 副本\dropshelf-tauri\src-tauri\target\debug\build\tauri-d5cf51a73d60648f\build_script_build-d5cf51a73d60648f.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-2.5.1\build.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-2.5.1\./mobile/proguard-tauri.pro

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-2.5.1\build.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-2.5.1\./mobile/proguard-tauri.pro:
