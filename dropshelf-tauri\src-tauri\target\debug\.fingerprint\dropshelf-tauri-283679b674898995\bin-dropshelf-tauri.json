{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"custom-protocol\"]", "target": 3622391092494350387, "profile": 8731458305071235362, "path": 4942398508502643691, "deps": [[1582828171158827377, "tauri_plugin_shell", false, 10363893476506806782], [6662138221317168675, "build_script_build", false, 16732821261121989105], [9538054652646069845, "tokio", false, 4103961411369455676], [9689903380558560274, "serde", false, 1695654867491534608], [10755362358622467486, "tauri", false, 14836831967397928173], [15367738274754116744, "serde_json", false, 1365797035968003195]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\dropshelf-tauri-283679b674898995\\dep-bin-dropshelf-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}