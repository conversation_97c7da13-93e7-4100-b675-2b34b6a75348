{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 15657897354478470176, "path": 10104357456112071365, "deps": [[376837177317575824, "build_script_build", false, 5214404843255277699], [4143744114649553716, "raw_window_handle", false, 7444958033936336087], [5986029879202738730, "log", false, 16679470630505032336], [10281541584571964250, "windows_sys", false, 7534827926581983306]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-16f670f970e991a1\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}