{"rustc": 16591470773350601817, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 6991718016828007708, "deps": [[3060637413840920116, "proc_macro2", false, 303717936180483038], [7341521034400937459, "tauri_codegen", false, 6001996485112435973], [11050281405049894993, "tauri_utils", false, 9863922467903923245], [13077543566650298139, "heck", false, 10476147102614925610], [17990358020177143287, "quote", false, 15754227166550723905], [18149961000318489080, "syn", false, 14297005405074787975]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-cd61149f2e738d2d\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}