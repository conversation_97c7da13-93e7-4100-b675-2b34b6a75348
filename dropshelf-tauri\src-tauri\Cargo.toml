[package]
name = "dropshelf-tauri"
version = "1.0.0"
description = "DropShelf - Windows productivity tool"
authors = ["DropShelf Team"]
license = "MIT"
repository = ""
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2.0", features = [] }

[dependencies]
tauri = { version = "2.0", features = ["shell-open"] }
tauri-plugin-shell = "2.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
rdev = "0.4"
winapi = { version = "0.3", features = [
    "winuser",
    "windef",
    "wingdi",
    "wincon",
    "consoleapi",
    "processenv",
    "handleapi",
    "fileapi",
    "errhandlingapi",
    "winerror",
    "shellapi",
    "objbase",
    "combaseapi",
    "shobjidl_core",
    "knownfolders",
    "shlobj",
    "shtypes",
    "minwindef",
    "libloaderapi",
    "synchapi",
    "winbase",
    "std",
    "impl-default",
] }
windows = { version = "0.52", features = [
    "Win32_Foundation",
    "Win32_System_LibraryLoader",
    "Win32_UI_WindowsAndMessaging",
    "Win32_Graphics_Gdi",
    "Win32_UI_Input_KeyboardAndMouse",
    "Win32_System_Threading",
    "Win32_UI_Shell",
] }

[features]
# This feature is used for production builds or when a dev server is not specified, DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]
