{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[6662138221317168675, "build_script_build", false, 16988588396916614118], [10755362358622467486, "build_script_build", false, 3382514209651873442], [1582828171158827377, "build_script_build", false, 9805579911996190199]], "local": [{"RerunIfChanged": {"output": "debug\\build\\dropshelf-tauri-458aa21313b15ca5\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": "{\"build\":{\"devUrl\":\"http://127.0.0.1:1430\"}}"}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}