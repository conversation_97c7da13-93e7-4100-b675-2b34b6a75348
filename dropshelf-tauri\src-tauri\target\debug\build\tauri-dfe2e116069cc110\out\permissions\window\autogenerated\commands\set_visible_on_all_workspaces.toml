# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-set-visible-on-all-workspaces"
description = "Enables the set_visible_on_all_workspaces command without any pre-configured scope."
commands.allow = ["set_visible_on_all_workspaces"]

[[permission]]
identifier = "deny-set-visible-on-all-workspaces"
description = "Denies the set_visible_on_all_workspaces command without any pre-configured scope."
commands.deny = ["set_visible_on_all_workspaces"]
