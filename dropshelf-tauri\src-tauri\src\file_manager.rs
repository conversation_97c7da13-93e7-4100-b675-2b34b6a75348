// 文件管理模块
// 处理文件的添加、移除和去重

use std::collections::HashSet;
use std::path::Path;

#[derive(Default)]
pub struct FileManager {
    file_paths: HashSet<String>,
}

impl FileManager {
    pub fn new() -> Self {
        Self {
            file_paths: HashSet::new(),
        }
    }
    
    /// 添加文件列表，自动去重
    /// 返回实际添加的文件数量
    pub fn add_files(&mut self, file_paths: Vec<String>) -> usize {
        let mut added_count = 0;
        
        for file_path in file_paths {
            // 验证文件路径
            if self.is_valid_file_path(&file_path) {
                // 标准化路径
                let normalized_path = self.normalize_path(&file_path);
                
                // 检查是否已存在
                if !self.file_paths.contains(&normalized_path) {
                    self.file_paths.insert(normalized_path.clone());
                    added_count += 1;
                    println!("📁 添加文件: {}", self.get_file_name(&normalized_path));
                } else {
                    println!("⚠️ 文件已存在: {}", self.get_file_name(&normalized_path));
                }
            } else {
                println!("❌ 无效文件路径: {}", file_path);
            }
        }
        
        if added_count > 0 {
            println!("📦 总计添加 {} 个文件，当前共 {} 个文件", added_count, self.file_paths.len());
        }
        
        added_count
    }
    
    /// 移除指定文件
    /// 返回是否成功移除
    pub fn remove_file(&mut self, file_path: &str) -> bool {
        let normalized_path = self.normalize_path(file_path);
        let removed = self.file_paths.remove(&normalized_path);
        
        if removed {
            println!("🗑️ 移除文件: {}", self.get_file_name(&normalized_path));
            println!("📦 当前共 {} 个文件", self.file_paths.len());
        }
        
        removed
    }
    
    /// 获取所有文件路径
    pub fn get_files(&self) -> Vec<String> {
        self.file_paths.iter().cloned().collect()
    }
    
    /// 获取文件数量
    pub fn get_file_count(&self) -> usize {
        self.file_paths.len()
    }
    
    /// 清空所有文件
    pub fn clear(&mut self) {
        let count = self.file_paths.len();
        self.file_paths.clear();
        println!("🧹 清空所有文件，共移除 {} 个文件", count);
    }
    
    /// 检查文件是否存在于列表中
    pub fn contains_file(&self, file_path: &str) -> bool {
        let normalized_path = self.normalize_path(file_path);
        self.file_paths.contains(&normalized_path)
    }
    
    /// 验证文件路径是否有效
    fn is_valid_file_path(&self, file_path: &str) -> bool {
        if file_path.is_empty() {
            return false;
        }
        
        let path = Path::new(file_path);
        
        // 检查路径是否存在
        if !path.exists() {
            return false;
        }
        
        // 检查是否为文件（不是目录）
        if !path.is_file() {
            return false;
        }
        
        true
    }
    
    /// 标准化文件路径
    fn normalize_path(&self, file_path: &str) -> String {
        // 将路径转换为绝对路径并标准化
        match Path::new(file_path).canonicalize() {
            Ok(canonical_path) => {
                canonical_path.to_string_lossy().to_string()
            }
            Err(_) => {
                // 如果无法标准化，返回原路径
                file_path.to_string()
            }
        }
    }
    
    /// 从完整路径中提取文件名
    fn get_file_name(&self, file_path: &str) -> String {
        Path::new(file_path)
            .file_name()
            .and_then(|name| name.to_str())
            .unwrap_or(file_path)
            .to_string()
    }
}
