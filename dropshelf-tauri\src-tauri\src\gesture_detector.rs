// 手势检测模块
// 检测鼠标抖动手势以召唤 DropShelf

use std::collections::VecDeque;
use std::time::{Duration, Instant};
use rdev::{listen, Event, EventType, Button};
use std::sync::{Arc, Mutex};
use std::thread;

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
struct MousePosition {
    x: f64,
    y: f64,
    timestamp: Instant,
}

pub struct GestureDetector {
    mouse_positions: Arc<Mutex<VecDeque<MousePosition>>>,
    is_mouse_pressed: Arc<Mutex<bool>>,
    last_shake_time: Arc<Mutex<Instant>>,
    
    // 配置参数
    shake_threshold: f64,
    max_shake_distance: f64,
    shake_time_window: Duration,
    min_direction_changes: usize,
    shake_cooldown: Duration,
}

impl GestureDetector {
    pub fn new() -> Self {
        let detector = Self {
            mouse_positions: Arc::new(Mutex::new(VecDeque::with_capacity(50))),
            is_mouse_pressed: Arc::new(Mutex::new(false)),
            last_shake_time: Arc::new(Mutex::new(Instant::now() - Duration::from_secs(10))),
            
            // 更敏感的设置，便于触发
            shake_threshold: 40.0,
            max_shake_distance: 200.0,
            shake_time_window: Duration::from_millis(400),
            min_direction_changes: 2,
            shake_cooldown: Duration::from_secs(1),
        };
        
        detector.start_mouse_listener();
        detector
    }
    
    fn start_mouse_listener(&self) {
        let positions = Arc::clone(&self.mouse_positions);
        let is_pressed = Arc::clone(&self.is_mouse_pressed);
        
        thread::spawn(move || {
            if let Err(error) = listen(move |event| {
                match event.event_type {
                    EventType::MouseMove { x, y } => {
                        // 只在鼠标按下时记录位置
                        let pressed = *is_pressed.lock().unwrap();
                        if pressed {
                            let mut positions = positions.lock().unwrap();
                            positions.push_back(MousePosition {
                                x,
                                y,
                                timestamp: Instant::now(),
                            });
                            
                            // 保持队列大小
                            if positions.len() > 50 {
                                positions.pop_front();
                            }
                        }
                    }
                    EventType::ButtonPress(Button::Left) => {
                        let mut pressed = is_pressed.lock().unwrap();
                        *pressed = true;
                        
                        // 清空之前的位置记录
                        let mut positions = positions.lock().unwrap();
                        positions.clear();
                        
                        println!("🖱️ 鼠标按下 - 开始监控抖动");
                    }
                    EventType::ButtonRelease(Button::Left) => {
                        let mut pressed = is_pressed.lock().unwrap();
                        *pressed = false;
                        
                        println!("🖱️ 鼠标松开 - 停止监控抖动");
                    }
                    _ => {}
                }
            }) {
                println!("❌ 鼠标监听器错误: {:?}", error);
            }
        });
    }
    
    pub fn check_for_shake(&self) -> Option<(i32, i32)> {
        // 检查冷却时间
        {
            let last_shake = *self.last_shake_time.lock().unwrap();
            if last_shake.elapsed() < self.shake_cooldown {
                return None;
            }
        }
        
        // 只在鼠标按下时检测
        let is_pressed = *self.is_mouse_pressed.lock().unwrap();
        if !is_pressed {
            return None;
        }
        
        let positions = self.mouse_positions.lock().unwrap();
        if positions.len() < 6 {
            return None;
        }
        
        // 获取时间窗口内的位置
        let recent_positions = self.get_recent_positions(&positions);
        if recent_positions.len() < 6 {
            return None;
        }
        
        // 检查是否为抖动模式
        if self.is_shake_pattern(&recent_positions) {
            // 获取当前鼠标位置
            if let Some(last_pos) = recent_positions.last() {
                let mut last_shake = self.last_shake_time.lock().unwrap();
                *last_shake = Instant::now();
                
                return Some((last_pos.x as i32, last_pos.y as i32));
            }
        }
        
        None
    }
    
    fn get_recent_positions(&self, positions: &VecDeque<MousePosition>) -> Vec<MousePosition> {
        let now = Instant::now();
        let cutoff_time = now - self.shake_time_window;
        
        positions
            .iter()
            .filter(|pos| pos.timestamp >= cutoff_time)
            .cloned()
            .collect()
    }
    
    fn is_shake_pattern(&self, positions: &[MousePosition]) -> bool {
        if positions.len() < 6 {
            return false;
        }
        
        // 计算总移动距离
        let mut total_distance = 0.0;
        for i in 1..positions.len() {
            let dx = positions[i].x - positions[i-1].x;
            let dy = positions[i].y - positions[i-1].y;
            total_distance += (dx*dx + dy*dy).sqrt();
        }
        
        // 检查移动距离是否在有效范围内
        if total_distance < self.shake_threshold {
            return false;
        }
        
        if total_distance > self.max_shake_distance {
            return false;
        }
        
        // 分析方向变化（水平抖动模式）
        let mut direction_changes = 0;
        let mut last_direction: Option<i32> = None;
        
        for i in 1..positions.len() {
            let dx = positions[i].x - positions[i-1].x;
            
            if dx.abs() > 2.0 { // 忽略微小移动
                let current_direction = if dx > 0.0 { 1 } else { -1 };
                
                if let Some(last_dir) = last_direction {
                    if current_direction != last_dir {
                        direction_changes += 1;
                    }
                }
                
                last_direction = Some(current_direction);
            }
        }
        
        // 检查是否有足够的方向变化（左-右-左模式）
        direction_changes >= self.min_direction_changes
    }
    
    #[allow(dead_code)]
    pub fn set_sensitivity(
        &mut self,
        threshold: Option<f64>,
        max_distance: Option<f64>,
        time_window: Option<Duration>,
        min_changes: Option<usize>,
    ) {
        if let Some(t) = threshold {
            self.shake_threshold = t;
            println!("抖动阈值设置为: {}", t);
        }
        
        if let Some(d) = max_distance {
            self.max_shake_distance = d;
            println!("最大抖动距离设置为: {}", d);
        }
        
        if let Some(w) = time_window {
            self.shake_time_window = w;
            println!("时间窗口设置为: {:?}", w);
        }
        
        if let Some(c) = min_changes {
            self.min_direction_changes = c;
            println!("最小方向变化设置为: {}", c);
        }
    }
}
