{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 6175278772369949323, "deps": [[376837177317575824, "softbuffer", false, 2230141744907140398], [442785307232013896, "tauri_runtime", false, 103214334099100806], [3150220818285335163, "url", false, 16151976542863992648], [3722963349756955755, "once_cell", false, 48240767122881265], [4143744114649553716, "raw_window_handle", false, 7444958033936336087], [5986029879202738730, "log", false, 16679470630505032336], [7752760652095876438, "build_script_build", false, 2996626512771605244], [8539587424388551196, "webview2_com", false, 17071827347805798439], [9010263965687315507, "http", false, 997752676421124756], [11050281405049894993, "tauri_utils", false, 349942949715664439], [13116089016666501665, "windows", false, 16591918237836803963], [13223659721939363523, "tao", false, 15245504308421975666], [14794439852947137341, "wry", false, 12930136265314436266]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-ec62e058a62d7a4b\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}