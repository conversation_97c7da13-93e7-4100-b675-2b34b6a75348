// DropShelf Tauri 主应用程序
// 使用 Rust + Tauri 重写的 Windows 生产力工具

#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, Emitter};
use std::sync::{Arc, Mutex};
use std::thread;
use std::time::Duration;

mod gesture_detector;
mod file_manager;

use gesture_detector::GestureDetector;
use file_manager::FileManager;

// 应用状态
#[derive(Default)]
struct AppState {
    is_shelf_visible: Arc<Mutex<bool>>,
    gesture_detector: Arc<Mutex<Option<GestureDetector>>>,
    file_manager: Arc<Mutex<FileManager>>,
}

// Tauri 命令：显示 Shelf
#[tauri::command]
async fn show_shelf_at(
    app: AppHandle,
    state: tauri::State<'_, AppState>,
    x: i32,
    y: i32,
) -> Result<(), String> {
    println!("🎉 显示 Shelf 在位置 ({}, {})", x, y);
    
    // 更新状态
    {
        let mut visible = state.is_shelf_visible.lock().unwrap();
        *visible = true;
    }
    
    // 获取主窗口
    if let Some(window) = app.get_webview_window("main") {
        // 设置窗口位置和大小
        let _ = window.set_position(tauri::Position::Physical(tauri::PhysicalPosition { x, y }));
        let _ = window.set_size(tauri::Size::Physical(tauri::PhysicalSize { width: 120, height: 80 }));
        
        // 显示窗口
        let _ = window.show();
        let _ = window.set_focus();
        
        println!("✅ Shelf 窗口已显示");
        Ok(())
    } else {
        Err("无法获取主窗口".to_string())
    }
}

// Tauri 命令：隐藏 Shelf
#[tauri::command]
async fn hide_shelf(
    app: AppHandle,
    state: tauri::State<'_, AppState>,
) -> Result<(), String> {
    println!("🙈 隐藏 Shelf");
    
    // 更新状态
    {
        let mut visible = state.is_shelf_visible.lock().unwrap();
        *visible = false;
    }
    
    // 获取主窗口并隐藏
    if let Some(window) = app.get_webview_window("main") {
        let _ = window.hide();
        println!("✅ Shelf 窗口已隐藏");
        Ok(())
    } else {
        Err("无法获取主窗口".to_string())
    }
}

// Tauri 命令：获取文件列表
#[tauri::command]
async fn get_files(state: tauri::State<'_, AppState>) -> Result<Vec<String>, String> {
    let file_manager = state.file_manager.lock().unwrap();
    Ok(file_manager.get_files())
}

// Tauri 命令：添加文件
#[tauri::command]
async fn add_files(
    state: tauri::State<'_, AppState>,
    file_paths: Vec<String>,
) -> Result<usize, String> {
    let mut file_manager = state.file_manager.lock().unwrap();
    let added_count = file_manager.add_files(file_paths);
    println!("📦 添加了 {} 个文件", added_count);
    Ok(added_count)
}

// Tauri 命令：移除文件
#[tauri::command]
async fn remove_file(
    state: tauri::State<'_, AppState>,
    file_path: String,
) -> Result<bool, String> {
    let mut file_manager = state.file_manager.lock().unwrap();
    let removed = file_manager.remove_file(&file_path);
    if removed {
        println!("🗑️ 移除文件: {}", file_path);
    }
    Ok(removed)
}

// 启动手势检测
fn start_gesture_detection(app: AppHandle, state: Arc<AppState>) {
    thread::spawn(move || {
        println!("🖱️ 启动手势检测线程");
        
        let detector = GestureDetector::new();
        
        // 存储检测器到状态
        {
            let mut gesture_detector = state.gesture_detector.lock().unwrap();
            *gesture_detector = Some(detector);
        }
        
        // 开始检测循环
        loop {
            // 检查是否应该停止检测（当 Shelf 可见时）
            let should_detect = {
                let visible = state.is_shelf_visible.lock().unwrap();
                !*visible
            };
            
            if should_detect {
                if let Some(ref mut detector) = *state.gesture_detector.lock().unwrap() {
                    if let Some((x, y)) = detector.check_for_shake() {
                        println!("🎉 检测到抖动手势在 ({}, {})", x, y);

                        // 直接显示窗口
                        if let Some(window) = app.get_webview_window("main") {
                            // 更新状态
                            {
                                let mut visible = state.is_shelf_visible.lock().unwrap();
                                *visible = true;
                            }

                            // 设置窗口位置和大小
                            let _ = window.set_position(tauri::Position::Physical(tauri::PhysicalPosition { x, y }));
                            let _ = window.set_size(tauri::Size::Physical(tauri::PhysicalSize { width: 120, height: 80 }));

                            // 显示窗口
                            let _ = window.show();
                            let _ = window.set_focus();

                            println!("✅ Shelf 窗口已显示在 ({}, {})", x, y);

                            // 同时发送事件到前端
                            let _ = app.emit("shake-detected", serde_json::json!({
                                "x": x,
                                "y": y
                            }));
                        }
                    }
                }
            }
            
            thread::sleep(Duration::from_millis(10));
        }
    });
}

fn main() {
    println!("🚀 启动 DropShelf Tauri 应用");
    
    tauri::Builder::default()
        .setup(|app| {
            println!("⚡ 设置应用程序");
            
            // 创建应用状态
            let state = Arc::new(AppState::default());
            app.manage(AppState::default());
            
            // 获取主窗口并初始隐藏
            if let Some(window) = app.get_webview_window("main") {
                let _ = window.hide();
                println!("✅ 主窗口已隐藏");
            }
            
            // 启动手势检测
            let app_handle = app.handle().clone();
            start_gesture_detection(app_handle, state);
            
            println!("✅ 应用程序设置完成");
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            show_shelf_at,
            hide_shelf,
            get_files,
            add_files,
            remove_file
        ])
        .run(tauri::generate_context!())
        .expect("运行 Tauri 应用时出错");
}
