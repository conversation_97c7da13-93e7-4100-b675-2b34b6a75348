{"rustc": 16591470773350601817, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 13318305459243126790, "path": 3367480689035872540, "deps": [[1615478164327904835, "pin_utils", false, 4671175212448925543], [1906322745568073236, "pin_project_lite", false, 12862050249561417846], [6955678925937229351, "slab", false, 16682578289744615482], [7620660491849607393, "futures_core", false, 10230466562405850784], [10565019901765856648, "futures_macro", false, 15232932743315299266], [16240732885093539806, "futures_task", false, 6135449894770943145]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-1f0270ad6c8a8df5\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}