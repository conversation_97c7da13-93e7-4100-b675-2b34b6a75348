{"rustc": 16591470773350601817, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 14192375337303177911, "deps": [[4899080583175475170, "semver", false, 12483454171726233177], [6913375703034175521, "schemars", false, 6427172178636291765], [7170110829644101142, "json_patch", false, 12137466159561387950], [9689903380558560274, "serde", false, 6321106767037116303], [11050281405049894993, "tauri_utils", false, 9863922467903923245], [12714016054753183456, "tauri_winres", false, 13419867937518685368], [13077543566650298139, "heck", false, 10476147102614925610], [13475171727366188400, "cargo_toml", false, 11677762894611416414], [13625485746686963219, "anyhow", false, 17869629950161525719], [15367738274754116744, "serde_json", false, 8383117432936140794], [15609422047640926750, "toml", false, 16738967182127994125], [15622660310229662834, "walkdir", false, 10049784400214970020], [16928111194414003569, "dirs", false, 4698535137041256793], [17155886227862585100, "glob", false, 9992052714327231586]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-ccda5d3e29608cfe\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}