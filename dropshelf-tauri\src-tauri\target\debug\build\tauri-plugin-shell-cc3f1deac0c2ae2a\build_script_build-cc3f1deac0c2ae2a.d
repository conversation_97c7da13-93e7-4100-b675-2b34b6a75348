C:\Users\<USER>\Desktop\试验项目\Aug_DropShelf_new - 副本\dropshelf-tauri\src-tauri\target\debug\build\tauri-plugin-shell-cc3f1deac0c2ae2a\build_script_build-cc3f1deac0c2ae2a.exe: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-shell-2.2.1\build.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-shell-2.2.1\src\scope_entry.rs

C:\Users\<USER>\Desktop\试验项目\Aug_DropShelf_new - 副本\dropshelf-tauri\src-tauri\target\debug\build\tauri-plugin-shell-cc3f1deac0c2ae2a\build_script_build-cc3f1deac0c2ae2a.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-shell-2.2.1\build.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-shell-2.2.1\src\scope_entry.rs

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-shell-2.2.1\build.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-shell-2.2.1\src\scope_entry.rs:
