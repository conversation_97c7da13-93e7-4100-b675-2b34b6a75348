{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\", \"macros\", \"std\"]", "declared_features": "[\"alloc\", \"base64\", \"chrono\", \"chrono_0_4\", \"default\", \"guide\", \"hashbrown_0_14\", \"hashbrown_0_15\", \"hex\", \"indexmap\", \"indexmap_1\", \"indexmap_2\", \"json\", \"macros\", \"schemars_0_8\", \"std\", \"time_0_3\"]", "target": 10448421281463538527, "profile": 5290030462671737236, "path": 9260253601645715706, "deps": [[6158493786865284961, "serde_with_macros", false, 14782315876239498085], [9689903380558560274, "serde", false, 1695654867491534608], [16257276029081467297, "serde_derive", false, 9750565721829292490]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\serde_with-c8daf43873fc4459\\dep-lib-serde_with", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}