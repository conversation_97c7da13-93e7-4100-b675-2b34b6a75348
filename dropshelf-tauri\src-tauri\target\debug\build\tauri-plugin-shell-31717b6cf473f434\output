cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=C:\Users\<USER>\Desktop\试验项目\Aug_DropShelf_new - 副本\dropshelf-tauri\src-tauri\target\debug\build\tauri-plugin-shell-31717b6cf473f434\out\tauri-plugin-shell-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_SCOPE_SCHEMA_PATH=C:\Users\<USER>\Desktop\试验项目\Aug_DropShelf_new - 副本\dropshelf-tauri\src-tauri\target\debug\build\tauri-plugin-shell-31717b6cf473f434\out\global-scope.json
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-shell-2.2.1\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
